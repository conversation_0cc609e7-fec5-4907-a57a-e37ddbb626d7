// lib: , url: YPi

// class id: 1048710, size: 0x8
class :: {

  static late final _sz _Jbg; // offset: 0xf98

  [closure] static wr bsc(dynamic, int, int) {
    // ** addr: 0x7eb994, size: -0x1
  }
}

// class id: 2664, size: 0x104, field offset: 0x104
class _zz extends Az {
}

// class id: 4485, size: 0x10, field offset: 0x8
class _yz extends Object {
}

// class id: 4486, size: 0x14, field offset: 0x8
abstract class _vz extends Object {

  [closure] void <anonymous closure>(dynamic, Ea) {
    // ** addr: 0x7ead2c, size: -0x1
  }
}

// class id: 4487, size: 0x18, field offset: 0x14
class _xz extends _vz {

  late (dynamic) => void _yPe; // offset: 0x14

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7edf18, size: -0x1
  }
}

// class id: 4488, size: 0x14, field offset: 0x14
class _wz extends _vz {
}

// class id: 4490, size: 0x50, field offset: 0x2c
class _tz extends uz {

  [closure] Future<Qe> <anonymous closure>(dynamic) async {
    // ** addr: 0x94127c, size: 0x15c
    // 0x94127c: EnterFrame
    //     0x94127c: stp             fp, lr, [SP, #-0x10]!
    //     0x941280: mov             fp, SP
    // 0x941284: AllocStack(0x38)
    //     0x941284: sub             SP, SP, #0x38
    // 0x941288: SetupParameters(_tz this /* r1 */)
    //     0x941288: stur            NULL, [fp, #-8]
    //     0x94128c: movz            x0, #0
    //     0x941290: add             x1, fp, w0, sxtw #2
    //     0x941294: ldr             x1, [x1, #0x10]
    //     0x941298: ldur            w2, [x1, #0x17]
    //     0x94129c: add             x2, x2, HEAP, lsl #32
    //     0x9412a0: stur            x2, [fp, #-0x10]
    // 0x9412a4: CheckStackOverflow
    //     0x9412a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9412a8: cmp             SP, x16
    //     0x9412ac: b.ls            #0x9413c0
    // 0x9412b0: InitAsync() -> Future<Qe>
    //     0x9412b0: add             x0, PP, #0x37, lsl #12  ; [pp+0x379e0] TypeArguments: <Qe>
    //     0x9412b4: ldr             x0, [x0, #0x9e0]
    //     0x9412b8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9412bc: r0 = InitLateStaticField(0xf98) // [YPi] ::_Jbg
    //     0x9412bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9412c0: ldr             x0, [x0, #0x1f30]
    //     0x9412c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9412c8: cmp             w0, w16
    //     0x9412cc: b.ne            #0x9412dc
    //     0x9412d0: add             x2, PP, #0xd, lsl #12  ; [pp+0xd670] Field <::._Jbg@857293038>: static late final (offset: 0xf98)
    //     0x9412d4: ldr             x2, [x2, #0x670]
    //     0x9412d8: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9412dc: stur            x0, [fp, #-0x20]
    // 0x9412e0: LoadField: r1 = r0->field_b
    //     0x9412e0: ldur            w1, [x0, #0xb]
    // 0x9412e4: DecompressPointer r1
    //     0x9412e4: add             x1, x1, HEAP, lsl #32
    // 0x9412e8: r16 = Sentinel
    //     0x9412e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9412ec: cmp             w1, w16
    // 0x9412f0: b.eq            #0x9413c8
    // 0x9412f4: ldur            x3, [fp, #-0x10]
    // 0x9412f8: LoadField: r4 = r3->field_27
    //     0x9412f8: ldur            w4, [x3, #0x27]
    // 0x9412fc: DecompressPointer r4
    //     0x9412fc: add             x4, x4, HEAP, lsl #32
    // 0x941300: mov             x2, x4
    // 0x941304: stur            x4, [fp, #-0x18]
    // 0x941308: r0 = __unknown_function__()
    //     0x941308: bl              #0x907528  ; [] ::__unknown_function__
    // 0x94130c: mov             x1, x0
    // 0x941310: stur            x1, [fp, #-0x28]
    // 0x941314: r0 = Await()
    //     0x941314: bl              #0x8c1bb8  ; AwaitStub
    // 0x941318: cmp             w0, NULL
    // 0x94131c: b.eq            #0x941368
    // 0x941320: r2 = LoadStaticField(0xbf8)
    //     0x941320: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x941324: ldr             x2, [x2, #0x17f0]
    // 0x941328: stur            x2, [fp, #-0x28]
    // 0x94132c: cmp             w2, NULL
    // 0x941330: b.eq            #0x9413d4
    // 0x941334: mov             x1, x0
    // 0x941338: r0 = call 0x79c93c
    //     0x941338: bl              #0x79c93c
    // 0x94133c: mov             x1, x0
    // 0x941340: stur            x1, [fp, #-0x30]
    // 0x941344: r0 = Await()
    //     0x941344: bl              #0x8c1bb8  ; AwaitStub
    // 0x941348: mov             x1, x0
    // 0x94134c: r2 = Closure: (int, int) => wr from Function 'bsc': static.
    //     0x94134c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37df8] Closure: (int, int) => wr from Function 'bsc': static. (0x2d7bd9db994)
    //     0x941350: ldr             x2, [x2, #0xdf8]
    // 0x941354: r0 = __unknown_function__()
    //     0x941354: bl              #0x93a0b0  ; [] ::__unknown_function__
    // 0x941358: mov             x1, x0
    // 0x94135c: stur            x1, [fp, #-0x28]
    // 0x941360: r0 = Await()
    //     0x941360: bl              #0x8c1bb8  ; AwaitStub
    // 0x941364: r0 = ReturnAsync()
    //     0x941364: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x941368: ldur            x0, [fp, #-0x20]
    // 0x94136c: LoadField: r1 = r0->field_b
    //     0x94136c: ldur            w1, [x0, #0xb]
    // 0x941370: DecompressPointer r1
    //     0x941370: add             x1, x1, HEAP, lsl #32
    // 0x941374: ldur            x2, [fp, #-0x18]
    // 0x941378: r0 = __unknown_function__()
    //     0x941378: bl              #0x8ee5d0  ; [] ::__unknown_function__
    // 0x94137c: mov             x1, x0
    // 0x941380: stur            x1, [fp, #-0x18]
    // 0x941384: r0 = Await()
    //     0x941384: bl              #0x8c1bb8  ; AwaitStub
    // 0x941388: ldur            x2, [fp, #-0x10]
    // 0x94138c: r1 = Function '<anonymous closure>':.
    //     0x94138c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e08] AnonymousClosure: (0x7ee350), in [YPi] _tz::<anonymous closure> (0x94127c)
    //     0x941390: ldr             x1, [x1, #0xe08]
    // 0x941394: r0 = AllocateClosure()
    //     0x941394: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x941398: str             x0, [SP]
    // 0x94139c: r0 = scheduleMicrotask()
    //     0x94139c: bl              #0x2ee57c  ; [dart:async] ::scheduleMicrotask
    // 0x9413a0: r0 = _Ka()
    //     0x9413a0: bl              #0x8bdc74  ; Allocate_KaStub -> _Ka (size=0xc)
    // 0x9413a4: mov             x1, x0
    // 0x9413a8: r0 = "NetworkImage is an empty cache"
    //     0x9413a8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37e10] "NetworkImage is an empty cache"
    //     0x9413ac: ldr             x0, [x0, #0xe10]
    // 0x9413b0: StoreField: r1->field_7 = r0
    //     0x9413b0: stur            w0, [x1, #7]
    // 0x9413b4: mov             x0, x1
    // 0x9413b8: r0 = Throw()
    //     0x9413b8: bl              #0x94dd08  ; ThrowStub
    // 0x9413bc: brk             #0
    // 0x9413c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9413c0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9413c4: b               #0x9412b0
    // 0x9413c8: r9 = Ebg
    //     0x9413c8: add             x9, PP, #0x19, lsl #12  ; [pp+0x19000] Field <<EMAIL>>: late final (offset: 0xc)
    //     0x9413cc: ldr             x9, [x9]
    // 0x9413d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9413d0: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9413d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9413d4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Qe> _Cbg(dynamic, Uint8List) {
    // ** addr: 0x7ee314, size: -0x1
  }
  [closure] void MHb(dynamic, IZ?, bool) {
    // ** addr: 0x7ee0a0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7ee350, size: -0x1
  }
  [closure] _xz <anonymous closure>(dynamic) {
    // ** addr: 0x7edc9c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7ee044, size: -0x1
  }
}

// class id: 4491, size: 0x20, field offset: 0x8
class _sz extends Object
    implements qz {

  late String _Gbg; // offset: 0x18
  late final String? Hbg; // offset: 0x1c
  late final pz<String, Uint8List> Ebg; // offset: 0xc
  late final String zbg; // offset: 0x10
}

// class id: 4492, size: 0x10, field offset: 0x8
class rz<X0, X1> extends Object
    implements pz<X0, X1> {
}

// class id: 4493, size: 0x8, field offset: 0x8
abstract class qz extends Object {

  static late final qz qkc; // offset: 0xf94
}

// class id: 4494, size: 0xc, field offset: 0x8
abstract class pz<X0, X1> extends Object {
}
