// lib: , url: WXi

// class id: 1049099, size: 0x8
class :: {

  static late final qga QCe; // offset: 0xb28
}

// class id: 1909, size: 0x8, field offset: 0x8
abstract class qga extends Object {
}

// class id: 1910, size: 0x14, field offset: 0x8
abstract class rga extends qga {

  [closure] Null <anonymous closure>(dynamic, Y0) {
    // ** addr: 0x807b54, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Object, ua) {
    // ** addr: 0x807acc, size: -0x1
  }
}

// class id: 1911, size: 0x14, field offset: 0x14
class sga extends rga {

  [closure] ByteData <anonymous closure>(dynamic, ByteData?) {
    // ** addr: 0x61612c, size: -0x1
  }
}
