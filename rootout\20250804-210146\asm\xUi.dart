// lib: , url: XUi

// class id: 1048944, size: 0x8
class :: {
}

// class id: 2914, size: 0xc, field offset: 0x8
//   const constructor, 
class rS extends _DF {
}

// class id: 3547, size: 0x14, field offset: 0x10
//   const constructor, 
class sS extends xI {

  const rS dyn:get:data(sS) {
    // ** addr: 0x8d4cd0, size: 0x28
    // 0x8d4cd0: ldr             x1, [SP]
    // 0x8d4cd4: LoadField: r0 = r1->field_f
    //     0x8d4cd4: ldur            w0, [x1, #0xf]
    // 0x8d4cd8: DecompressPointer r0
    //     0x8d4cd8: add             x0, x0, HEAP, lsl #32
    // 0x8d4cdc: ret
    //     0x8d4cdc: ret             
  }
}
