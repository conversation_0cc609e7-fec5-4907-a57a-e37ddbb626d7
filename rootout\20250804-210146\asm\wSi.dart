// lib: , url: WSi

// class id: 1048846, size: 0x8
class :: {
}

// class id: 2668, size: 0x10, field offset: 0x8
//   const constructor, 
class _CI extends Object {

  HG field_8;
  HG field_c;
}

// class id: 2669, size: 0x24, field offset: 0x8
//   const constructor, 
class _BI extends Object {

  HG field_c;
  HG field_10;
  HG field_14;
  HG field_18;
  bool field_1c;
  _CI field_20;
}

// class id: 2670, size: 0x24, field offset: 0x8
//   const constructor, 
class zI extends Object {
}

// class id: 2671, size: 0x24, field offset: 0x24
//   const constructor, transformed mixin,
abstract class _yI extends zI
     with GG {
}

// class id: 2672, size: 0x28, field offset: 0x24
//   const constructor, 
class AI extends _yI {

  _BI field_24;
}

// class id: 3026, size: 0x34, field offset: 0x30
//   const constructor, 
class _DI extends tI {
}

// class id: 3555, size: 0x14, field offset: 0x10
//   const constructor, 
class _wI extends xI {
}

// class id: 3800, size: 0x14, field offset: 0xc
//   const constructor, 
class vI extends Kt {
}
