// lib: , url: ZRi

// class id: 1048798, size: 0x8
class :: {
}

// class id: 3280, size: 0x38, field offset: 0x1c
class DE extends EE<dynamic> {

  late vZa _NJc; // offset: 0x20

  [closure] bool _Nif(dynamic, Sra) {
    // ** addr: 0x5b4f20, size: -0x1
  }
  [closure] bool _AAc(dynamic, Sta) {
    // ** addr: 0x5b4620, size: -0x1
  }
  [closure] void _Bkd(dynamic) {
    // ** addr: 0x51e118, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x51e6f0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x51e648, size: -0x1
  }
}

// class id: 4031, size: 0x3c, field offset: 0xc
//   const constructor, 
class CE extends It {
}
