// lib: , url: Ydj

// class id: 1049405, size: 0x8
class :: {

  static late final RegExp rdg; // offset: 0xfd0
  static late final RegExp token; // offset: 0xfbc
  static late final RegExp _odg; // offset: 0xfc4
  static late final RegExp _pdg; // offset: 0xfc8
  static late final RegExp _ndg; // offset: 0xfc0
  static late final RegExp qdg; // offset: 0xfcc

  [closure] static String <anonymous closure>(dynamic, wa) {
    // ** addr: 0x40ca8c, size: -0x1
  }
}
