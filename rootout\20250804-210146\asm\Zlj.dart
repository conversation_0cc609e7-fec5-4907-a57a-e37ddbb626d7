// lib: , url: Zlj

// class id: 1049815, size: 0x8
class :: {
}

// class id: 5728, size: 0x18, field offset: 0xc
abstract class MZa<X0> extends B<X0> {

  int length(MZa<X0>) {
    // ** addr: 0x8fdde4, size: 0x48
    // 0x8fdde4: EnterFrame
    //     0x8fdde4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdde8: mov             fp, SP
    // 0x8fddec: ldr             x2, [fp, #0x10]
    // 0x8fddf0: LoadField: r3 = r2->field_f
    //     0x8fddf0: ldur            x3, [x2, #0xf]
    // 0x8fddf4: r0 = BoxInt64Instr(r3)
    //     0x8fddf4: sbfiz           x0, x3, #1, #0x1f
    //     0x8fddf8: cmp             x3, x0, asr #1
    //     0x8fddfc: b.eq            #0x8fde08
    //     0x8fde00: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fde04: stur            x3, [x0, #7]
    // 0x8fde08: LeaveFrame
    //     0x8fde08: mov             SP, fp
    //     0x8fde0c: ldp             fp, lr, [SP], #0x10
    // 0x8fde10: ret
    //     0x8fde10: ret             
  }
  void []=(MZa<X0>, int, X0) {
    // ** addr: 0x8bb780, size: 0x94
    // 0x8bb780: EnterFrame
    //     0x8bb780: stp             fp, lr, [SP, #-0x10]!
    //     0x8bb784: mov             fp, SP
    // 0x8bb788: AllocStack(0x18)
    //     0x8bb788: sub             SP, SP, #0x18
    // 0x8bb78c: CheckStackOverflow
    //     0x8bb78c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bb790: cmp             SP, x16
    //     0x8bb794: b.ls            #0x8bb7f4
    // 0x8bb798: ldr             x0, [fp, #0x18]
    // 0x8bb79c: r2 = Null
    //     0x8bb79c: mov             x2, NULL
    // 0x8bb7a0: r1 = Null
    //     0x8bb7a0: mov             x1, NULL
    // 0x8bb7a4: branchIfSmi(r0, 0x8bb7cc)
    //     0x8bb7a4: tbz             w0, #0, #0x8bb7cc
    // 0x8bb7a8: r4 = LoadClassIdInstr(r0)
    //     0x8bb7a8: ldur            x4, [x0, #-1]
    //     0x8bb7ac: ubfx            x4, x4, #0xc, #0x14
    // 0x8bb7b0: sub             x4, x4, #0x3b
    // 0x8bb7b4: cmp             x4, #1
    // 0x8bb7b8: b.ls            #0x8bb7cc
    // 0x8bb7bc: r8 = int
    //     0x8bb7bc: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8bb7c0: r3 = Null
    //     0x8bb7c0: add             x3, PP, #0x23, lsl #12  ; [pp+0x23540] Null
    //     0x8bb7c4: ldr             x3, [x3, #0x540]
    // 0x8bb7c8: r0 = int()
    //     0x8bb7c8: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8bb7cc: ldr             x16, [fp, #0x20]
    // 0x8bb7d0: ldr             lr, [fp, #0x18]
    // 0x8bb7d4: stp             lr, x16, [SP, #8]
    // 0x8bb7d8: ldr             x16, [fp, #0x10]
    // 0x8bb7dc: str             x16, [SP]
    // 0x8bb7e0: r0 = call 0x308a9c
    //     0x8bb7e0: bl              #0x308a9c
    // 0x8bb7e4: r0 = Null
    //     0x8bb7e4: mov             x0, NULL
    // 0x8bb7e8: LeaveFrame
    //     0x8bb7e8: mov             SP, fp
    //     0x8bb7ec: ldp             fp, lr, [SP], #0x10
    // 0x8bb7f0: ret
    //     0x8bb7f0: ret             
    // 0x8bb7f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bb7f4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bb7f8: b               #0x8bb798
  }
  X0 [](MZa<X0>, int) {
    // ** addr: 0x8bb814, size: 0x88
    // 0x8bb814: EnterFrame
    //     0x8bb814: stp             fp, lr, [SP, #-0x10]!
    //     0x8bb818: mov             fp, SP
    // 0x8bb81c: AllocStack(0x10)
    //     0x8bb81c: sub             SP, SP, #0x10
    // 0x8bb820: CheckStackOverflow
    //     0x8bb820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bb824: cmp             SP, x16
    //     0x8bb828: b.ls            #0x8bb87c
    // 0x8bb82c: ldr             x0, [fp, #0x10]
    // 0x8bb830: r2 = Null
    //     0x8bb830: mov             x2, NULL
    // 0x8bb834: r1 = Null
    //     0x8bb834: mov             x1, NULL
    // 0x8bb838: branchIfSmi(r0, 0x8bb860)
    //     0x8bb838: tbz             w0, #0, #0x8bb860
    // 0x8bb83c: r4 = LoadClassIdInstr(r0)
    //     0x8bb83c: ldur            x4, [x0, #-1]
    //     0x8bb840: ubfx            x4, x4, #0xc, #0x14
    // 0x8bb844: sub             x4, x4, #0x3b
    // 0x8bb848: cmp             x4, #1
    // 0x8bb84c: b.ls            #0x8bb860
    // 0x8bb850: r8 = int
    //     0x8bb850: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8bb854: r3 = Null
    //     0x8bb854: add             x3, PP, #0xe, lsl #12  ; [pp+0xe4e0] Null
    //     0x8bb858: ldr             x3, [x3, #0x4e0]
    // 0x8bb85c: r0 = int()
    //     0x8bb85c: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8bb860: ldr             x16, [fp, #0x18]
    // 0x8bb864: ldr             lr, [fp, #0x10]
    // 0x8bb868: stp             lr, x16, [SP]
    // 0x8bb86c: r0 = call 0x30ae78
    //     0x8bb86c: bl              #0x30ae78
    // 0x8bb870: LeaveFrame
    //     0x8bb870: mov             SP, fp
    //     0x8bb874: ldp             fp, lr, [SP], #0x10
    // 0x8bb878: ret
    //     0x8bb878: ret             
    // 0x8bb87c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bb87c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bb880: b               #0x8bb82c
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x2d8c90, size: -0x1
  }
}

// class id: 5729, size: 0x18, field offset: 0x18
abstract class _NZa extends MZa<dynamic> {
}

// class id: 5730, size: 0x18, field offset: 0x18
class OZa extends _NZa {
}
