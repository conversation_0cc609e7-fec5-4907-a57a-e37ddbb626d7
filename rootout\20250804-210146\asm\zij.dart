// lib: npj, url: Zij

// class id: 1049632, size: 0x8
class :: {
}

// class id: 823, size: 0x18, field offset: 0x18
class SSa extends VSa
    implements fSa {

  static late final vWa iog; // offset: 0x1110
  static late final RegExp _zqg; // offset: 0x110c

  [closure] static (dynamic) => SSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x474b1c, size: -0x1
  }
  [closure] static SSa <anonymous closure>(dynamic) {
    // ** addr: 0x474b70, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x474a90, size: -0x1
  }
}
