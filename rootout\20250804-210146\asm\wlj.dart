// lib: , url: Wlj

// class id: 1049812, size: 0x8
class :: {
}

// class id: 622, size: 0x8, field offset: 0x8
class J<PERSON><PERSON> extends Object
    implements Ja {
}

// class id: 623, size: 0x8, field offset: 0x8
class I<PERSON><PERSON> extends Object {

  static late final Map<String, String> _zUg; // offset: 0x1430

  HZa [](IZa, String) {
    // ** addr: 0x90da7c, size: 0x84
    // 0x90da7c: EnterFrame
    //     0x90da7c: stp             fp, lr, [SP, #-0x10]!
    //     0x90da80: mov             fp, SP
    // 0x90da84: CheckStackOverflow
    //     0x90da84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90da88: cmp             SP, x16
    //     0x90da8c: b.ls            #0x90dae0
    // 0x90da90: ldr             x0, [fp, #0x10]
    // 0x90da94: r2 = Null
    //     0x90da94: mov             x2, NULL
    // 0x90da98: r1 = Null
    //     0x90da98: mov             x1, NULL
    // 0x90da9c: r4 = 59
    //     0x90da9c: movz            x4, #0x3b
    // 0x90daa0: branchIfSmi(r0, 0x90daac)
    //     0x90daa0: tbz             w0, #0, #0x90daac
    // 0x90daa4: r4 = LoadClassIdInstr(r0)
    //     0x90daa4: ldur            x4, [x0, #-1]
    //     0x90daa8: ubfx            x4, x4, #0xc, #0x14
    // 0x90daac: sub             x4, x4, #0x5d
    // 0x90dab0: cmp             x4, #1
    // 0x90dab4: b.ls            #0x90dac8
    // 0x90dab8: r8 = String
    //     0x90dab8: ldr             x8, [PP, #0x328]  ; [pp+0x328] Type: String
    // 0x90dabc: r3 = Null
    //     0x90dabc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32638] Null
    //     0x90dac0: ldr             x3, [x3, #0x638]
    // 0x90dac4: r0 = String()
    //     0x90dac4: bl              #0x958918  ; IsType_String_Stub
    // 0x90dac8: ldr             x1, [fp, #0x18]
    // 0x90dacc: ldr             x2, [fp, #0x10]
    // 0x90dad0: r0 = call 0x598a88
    //     0x90dad0: bl              #0x598a88
    // 0x90dad4: LeaveFrame
    //     0x90dad4: mov             SP, fp
    //     0x90dad8: ldp             fp, lr, [SP], #0x10
    // 0x90dadc: ret
    //     0x90dadc: ret             
    // 0x90dae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90dae0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90dae4: b               #0x90da90
  }
}

// class id: 624, size: 0xc, field offset: 0x8
class HZa extends Object {
}
