// lib: , url: VPi

// class id: 1048707, size: 0x8
class :: {
}

// class id: 3304, size: 0x2c, field offset: 0x14
class _lz extends Mt<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x5aca24, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x5ab3a0, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x5ab358, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x5ab100, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x5ab150, size: -0x1
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x5ac9d8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5ac6b4, size: -0x1
  }
}

// class id: 4045, size: 0x10, field offset: 0xc
class kz extends It {
}
