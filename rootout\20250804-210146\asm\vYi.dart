// lib: , url: vYi

// class id: 1049126, size: 0x8
class :: {
}

// class id: 1830, size: 0x24, field offset: 0x8
class gia extends Object {

  [closure] void _Fqe(dynamic, gia) {
    // ** addr: 0x519a88, size: -0x1
  }
  [closure] void _wHe(dynamic, gia) {
    // ** addr: 0x519b50, size: -0x1
  }
  [closure] Map<Object?, Object?> <anonymous closure>(dynamic) {
    // ** addr: 0x5198a0, size: -0x1
  }
  [closure] List<gia> <anonymous closure>(dynamic) {
    // ** addr: 0x519e90, size: -0x1
  }
}

// class id: 4532, size: 0x3c, field offset: 0x24
class Iga extends Pu {

  [closure] Future<void> _eHe(dynamic, MethodCall) {
    // ** addr: 0x61b81c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Ea) {
    // ** addr: 0x61bd84, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Ea) {
    // ** addr: 0x5193c0, size: -0x1
  }
}
