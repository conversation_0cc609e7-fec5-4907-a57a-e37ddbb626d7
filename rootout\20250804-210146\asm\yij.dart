// lib: Bpj, url: Yij

// class id: 1049631, size: 0x8
class :: {
}

// class id: 805, size: 0x70, field offset: 0x48
class zTa extends vTa
    implements dB {

  static late final vWa iog; // offset: 0x1158
  static late final RegExp _nrg; // offset: 0x1154
  static late final zWa _org; // offset: 0x115c

  [closure] static (dynamic) => zTa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x475af0, size: -0x1
  }
  [closure] static zTa <anonymous closure>(dynamic) {
    // ** addr: 0x475b44, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x475a64, size: -0x1
  }
}
