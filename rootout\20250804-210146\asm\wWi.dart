// lib: , url: WWi

// class id: 1049036, size: 0x8
class :: {
}

// class id: 2262, size: 0x20, field offset: 0x8
//   const constructor, 
class PZ extends Object {

  _TwoByteString field_8;
  bool field_14;
  bool field_18;
  _ImmutableList<zs> field_1c;
}

// class id: 2263, size: 0x10, field offset: 0x8
class OZ extends Object {
}

// class id: 3417, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class QZ extends zJ {

  [closure] bool <anonymous closure>(dynamic, QZ) {
    // ** addr: 0x343bb4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, QZ) {
    // ** addr: 0x391864, size: -0x1
  }
}
