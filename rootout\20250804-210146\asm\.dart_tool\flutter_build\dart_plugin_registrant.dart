// lib: , url: file:///var/lib/jenkins/workspace/51cg/.dart_tool/flutter_build/dart_plugin_registrant.dart

// class id: 1048593, size: 0x8
class :: {
}

// class id: 4645, size: 0x8, field offset: 0x8
class _PluginRegistrant extends Object {

  static void register() {
    // ** addr: 0x9509dc, size: 0x204
    // 0x9509dc: EnterFrame
    //     0x9509dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9509e0: mov             fp, SP
    // 0x9509e4: AllocStack(0x38)
    //     0x9509e4: sub             SP, SP, #0x38
    // 0x9509e8: CheckStackOverflow
    //     0x9509e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9509ec: cmp             SP, x16
    //     0x9509f0: b.ls            #0x950bd8
    // 0x9509f4: r0 = call 0x8b5908
    //     0x9509f4: bl              #0x8b5908
    // 0x9509f8: r0 = Null
    //     0x9509f8: mov             x0, NULL
    // 0x9509fc: b               #0x950a40
    // 0x950a00: sub             SP, fp, #0x38
    // 0x950a04: stur            x0, [fp, #-0x30]
    // 0x950a08: r1 = Null
    //     0x950a08: mov             x1, NULL
    // 0x950a0c: r2 = 6
    //     0x950a0c: movz            x2, #0x6
    // 0x950a10: r0 = AllocateArray()
    //     0x950a10: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950a14: r17 = "`image_picker_android` threw an error: "
    //     0x950a14: ldr             x17, [PP, #0x70]  ; [pp+0x70] "`image_picker_android` threw an error: "
    // 0x950a18: StoreField: r0->field_f = r17
    //     0x950a18: stur            w17, [x0, #0xf]
    // 0x950a1c: ldur            x1, [fp, #-0x30]
    // 0x950a20: StoreField: r0->field_13 = r1
    //     0x950a20: stur            w1, [x0, #0x13]
    // 0x950a24: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950a24: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950a28: ArrayStore: r0[0] = r17  ; List_4
    //     0x950a28: stur            w17, [x0, #0x17]
    // 0x950a2c: str             x0, [SP]
    // 0x950a30: r0 = _interpolate()
    //     0x950a30: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950a34: mov             x1, x0
    // 0x950a38: r0 = call 0x4cd264
    //     0x950a38: bl              #0x4cd264
    // 0x950a3c: ldur            x0, [fp, #-0x30]
    // 0x950a40: stur            x0, [fp, #-0x30]
    // 0x950a44: r0 = call 0x8b5808
    //     0x950a44: bl              #0x8b5808
    // 0x950a48: ldur            x0, [fp, #-0x30]
    // 0x950a4c: b               #0x950a90
    // 0x950a50: sub             SP, fp, #0x38
    // 0x950a54: stur            x0, [fp, #-0x30]
    // 0x950a58: r1 = Null
    //     0x950a58: mov             x1, NULL
    // 0x950a5c: r2 = 6
    //     0x950a5c: movz            x2, #0x6
    // 0x950a60: r0 = AllocateArray()
    //     0x950a60: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950a64: r17 = "`path_provider_android` threw an error: "
    //     0x950a64: ldr             x17, [PP, #0x80]  ; [pp+0x80] "`path_provider_android` threw an error: "
    // 0x950a68: StoreField: r0->field_f = r17
    //     0x950a68: stur            w17, [x0, #0xf]
    // 0x950a6c: ldur            x1, [fp, #-0x30]
    // 0x950a70: StoreField: r0->field_13 = r1
    //     0x950a70: stur            w1, [x0, #0x13]
    // 0x950a74: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950a74: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950a78: ArrayStore: r0[0] = r17  ; List_4
    //     0x950a78: stur            w17, [x0, #0x17]
    // 0x950a7c: str             x0, [SP]
    // 0x950a80: r0 = _interpolate()
    //     0x950a80: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950a84: mov             x1, x0
    // 0x950a88: r0 = call 0x4cd264
    //     0x950a88: bl              #0x4cd264
    // 0x950a8c: ldur            x0, [fp, #-0x30]
    // 0x950a90: stur            x0, [fp, #-0x30]
    // 0x950a94: r0 = call 0x8b5718
    //     0x950a94: bl              #0x8b5718
    // 0x950a98: ldur            x0, [fp, #-0x30]
    // 0x950a9c: b               #0x950ae0
    // 0x950aa0: sub             SP, fp, #0x38
    // 0x950aa4: stur            x0, [fp, #-0x30]
    // 0x950aa8: r1 = Null
    //     0x950aa8: mov             x1, NULL
    // 0x950aac: r2 = 6
    //     0x950aac: movz            x2, #0x6
    // 0x950ab0: r0 = AllocateArray()
    //     0x950ab0: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950ab4: r17 = "`shared_preferences_android` threw an error: "
    //     0x950ab4: ldr             x17, [PP, #0x88]  ; [pp+0x88] "`shared_preferences_android` threw an error: "
    // 0x950ab8: StoreField: r0->field_f = r17
    //     0x950ab8: stur            w17, [x0, #0xf]
    // 0x950abc: ldur            x1, [fp, #-0x30]
    // 0x950ac0: StoreField: r0->field_13 = r1
    //     0x950ac0: stur            w1, [x0, #0x13]
    // 0x950ac4: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950ac4: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950ac8: ArrayStore: r0[0] = r17  ; List_4
    //     0x950ac8: stur            w17, [x0, #0x17]
    // 0x950acc: str             x0, [SP]
    // 0x950ad0: r0 = _interpolate()
    //     0x950ad0: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950ad4: mov             x1, x0
    // 0x950ad8: r0 = call 0x4cd264
    //     0x950ad8: bl              #0x4cd264
    // 0x950adc: ldur            x0, [fp, #-0x30]
    // 0x950ae0: stur            x0, [fp, #-0x30]
    // 0x950ae4: r0 = call 0x8b5618
    //     0x950ae4: bl              #0x8b5618
    // 0x950ae8: ldur            x0, [fp, #-0x30]
    // 0x950aec: b               #0x950b30
    // 0x950af0: sub             SP, fp, #0x38
    // 0x950af4: stur            x0, [fp, #-0x30]
    // 0x950af8: r1 = Null
    //     0x950af8: mov             x1, NULL
    // 0x950afc: r2 = 6
    //     0x950afc: movz            x2, #0x6
    // 0x950b00: r0 = AllocateArray()
    //     0x950b00: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950b04: r17 = "`url_launcher_android` threw an error: "
    //     0x950b04: ldr             x17, [PP, #0x90]  ; [pp+0x90] "`url_launcher_android` threw an error: "
    // 0x950b08: StoreField: r0->field_f = r17
    //     0x950b08: stur            w17, [x0, #0xf]
    // 0x950b0c: ldur            x1, [fp, #-0x30]
    // 0x950b10: StoreField: r0->field_13 = r1
    //     0x950b10: stur            w1, [x0, #0x13]
    // 0x950b14: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950b14: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950b18: ArrayStore: r0[0] = r17  ; List_4
    //     0x950b18: stur            w17, [x0, #0x17]
    // 0x950b1c: str             x0, [SP]
    // 0x950b20: r0 = _interpolate()
    //     0x950b20: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950b24: mov             x1, x0
    // 0x950b28: r0 = call 0x4cd264
    //     0x950b28: bl              #0x4cd264
    // 0x950b2c: ldur            x0, [fp, #-0x30]
    // 0x950b30: stur            x0, [fp, #-0x30]
    // 0x950b34: r0 = call 0x8b5520
    //     0x950b34: bl              #0x8b5520
    // 0x950b38: ldur            x0, [fp, #-0x30]
    // 0x950b3c: b               #0x950b80
    // 0x950b40: sub             SP, fp, #0x38
    // 0x950b44: stur            x0, [fp, #-0x30]
    // 0x950b48: r1 = Null
    //     0x950b48: mov             x1, NULL
    // 0x950b4c: r2 = 6
    //     0x950b4c: movz            x2, #0x6
    // 0x950b50: r0 = AllocateArray()
    //     0x950b50: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950b54: r17 = "`webview_flutter_android` threw an error: "
    //     0x950b54: ldr             x17, [PP, #0x98]  ; [pp+0x98] "`webview_flutter_android` threw an error: "
    // 0x950b58: StoreField: r0->field_f = r17
    //     0x950b58: stur            w17, [x0, #0xf]
    // 0x950b5c: ldur            x1, [fp, #-0x30]
    // 0x950b60: StoreField: r0->field_13 = r1
    //     0x950b60: stur            w1, [x0, #0x13]
    // 0x950b64: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950b64: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950b68: ArrayStore: r0[0] = r17  ; List_4
    //     0x950b68: stur            w17, [x0, #0x17]
    // 0x950b6c: str             x0, [SP]
    // 0x950b70: r0 = _interpolate()
    //     0x950b70: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950b74: mov             x1, x0
    // 0x950b78: r0 = call 0x4cd264
    //     0x950b78: bl              #0x4cd264
    // 0x950b7c: ldur            x0, [fp, #-0x30]
    // 0x950b80: stur            x0, [fp, #-0x30]
    // 0x950b84: r0 = call 0x8b5420
    //     0x950b84: bl              #0x8b5420
    // 0x950b88: b               #0x950bc8
    // 0x950b8c: sub             SP, fp, #0x38
    // 0x950b90: stur            x0, [fp, #-0x30]
    // 0x950b94: r1 = Null
    //     0x950b94: mov             x1, NULL
    // 0x950b98: r2 = 6
    //     0x950b98: movz            x2, #0x6
    // 0x950b9c: r0 = AllocateArray()
    //     0x950b9c: bl              #0x94fa24  ; AllocateArrayStub
    // 0x950ba0: r17 = "`video_player_android` threw an error: "
    //     0x950ba0: ldr             x17, [PP, #0xa0]  ; [pp+0xa0] "`video_player_android` threw an error: "
    // 0x950ba4: StoreField: r0->field_f = r17
    //     0x950ba4: stur            w17, [x0, #0xf]
    // 0x950ba8: ldur            x1, [fp, #-0x30]
    // 0x950bac: StoreField: r0->field_13 = r1
    //     0x950bac: stur            w1, [x0, #0x13]
    // 0x950bb0: r17 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0x950bb0: ldr             x17, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0x950bb4: ArrayStore: r0[0] = r17  ; List_4
    //     0x950bb4: stur            w17, [x0, #0x17]
    // 0x950bb8: str             x0, [SP]
    // 0x950bbc: r0 = _interpolate()
    //     0x950bbc: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x950bc0: mov             x1, x0
    // 0x950bc4: r0 = call 0x4cd264
    //     0x950bc4: bl              #0x4cd264
    // 0x950bc8: r0 = Null
    //     0x950bc8: mov             x0, NULL
    // 0x950bcc: LeaveFrame
    //     0x950bcc: mov             SP, fp
    //     0x950bd0: ldp             fp, lr, [SP], #0x10
    // 0x950bd4: ret
    //     0x950bd4: ret             
    // 0x950bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x950bd8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x950bdc: b               #0x9509f4
  }
}
