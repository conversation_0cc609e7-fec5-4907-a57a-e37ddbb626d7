// lib: , url: YXi

// class id: 1049103, size: 0x8
class :: {
}

// class id: 1899, size: 0x8, field offset: 0x8
abstract class _Jga extends Object
    implements gJ, Daa {
}

// class id: 1900, size: 0x8, field offset: 0x8
abstract class PY extends _Jga {

  [closure] static List<DJ> _eDe(dynamic, String) {
    // ** addr: 0x616284, size: -0x1
  }
}

// class id: 1902, size: 0x8, field offset: 0x8
//   const constructor, 
class _Kga extends Ega {

  [closure] Future<void> <anonymous closure>(dynamic, ByteData?, (dynamic, ByteData?) => void) async {
    // ** addr: 0x944a90, size: 0x174
    // 0x944a90: EnterFrame
    //     0x944a90: stp             fp, lr, [SP, #-0x10]!
    //     0x944a94: mov             fp, SP
    // 0x944a98: AllocStack(0xa0)
    //     0x944a98: sub             SP, SP, #0xa0
    // 0x944a9c: SetupParameters(_Kga this /* r1, fp-0x90 */, dynamic _ /* r2, fp-0x88 */, dynamic _ /* r3, fp-0x80 */)
    //     0x944a9c: stur            NULL, [fp, #-8]
    //     0x944aa0: movz            x0, #0
    //     0x944aa4: add             x1, fp, w0, sxtw #2
    //     0x944aa8: ldr             x1, [x1, #0x20]
    //     0x944aac: stur            x1, [fp, #-0x90]
    //     0x944ab0: add             x2, fp, w0, sxtw #2
    //     0x944ab4: ldr             x2, [x2, #0x18]
    //     0x944ab8: stur            x2, [fp, #-0x88]
    //     0x944abc: add             x3, fp, w0, sxtw #2
    //     0x944ac0: ldr             x3, [x3, #0x10]
    //     0x944ac4: stur            x3, [fp, #-0x80]
    //     0x944ac8: ldur            w4, [x1, #0x17]
    //     0x944acc: add             x4, x4, HEAP, lsl #32
    //     0x944ad0: stur            x4, [fp, #-0x78]
    // 0x944ad4: CheckStackOverflow
    //     0x944ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944ad8: cmp             SP, x16
    //     0x944adc: b.ls            #0x944bf0
    // 0x944ae0: InitAsync() -> Future<void?>
    //     0x944ae0: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x944ae4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x944ae8: ldur            x0, [fp, #-0x78]
    // 0x944aec: LoadField: r1 = r0->field_f
    //     0x944aec: ldur            w1, [x0, #0xf]
    // 0x944af0: DecompressPointer r1
    //     0x944af0: add             x1, x1, HEAP, lsl #32
    // 0x944af4: stur            x1, [fp, #-0x90]
    // 0x944af8: cmp             w1, NULL
    // 0x944afc: b.eq            #0x944bf8
    // 0x944b00: ldur            x16, [fp, #-0x88]
    // 0x944b04: stp             x16, x1, [SP]
    // 0x944b08: mov             x0, x1
    // 0x944b0c: ClosureCall
    //     0x944b0c: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x944b10: ldur            x2, [x0, #0x1f]
    //     0x944b14: blr             x2
    // 0x944b18: mov             x2, x0
    // 0x944b1c: r1 = <ByteData?>
    //     0x944b1c: ldr             x1, [PP, #0x5e8]  ; [pp+0x5e8] TypeArguments: <ByteData?>
    // 0x944b20: stur            x2, [fp, #-0x78]
    // 0x944b24: r0 = AwaitWithTypeCheck()
    //     0x944b24: bl              #0x8c3a5c  ; AwaitWithTypeCheckStub
    // 0x944b28: ldur            x1, [fp, #-0x80]
    // 0x944b2c: b               #0x944b90
    // 0x944b30: sub             SP, fp, #0xa0
    // 0x944b34: mov             x2, x0
    // 0x944b38: stur            x0, [fp, #-0x78]
    // 0x944b3c: mov             x0, x1
    // 0x944b40: stur            x1, [fp, #-0x80]
    // 0x944b44: r1 = <List<Object>>
    //     0x944b44: ldr             x1, [PP, #0x2468]  ; [pp+0x2468] TypeArguments: <List<Object>>
    // 0x944b48: r0 = OI()
    //     0x944b48: bl              #0x8c4f9c  ; AllocateOIStub -> OI (size=0x2c)
    // 0x944b4c: mov             x1, x0
    // 0x944b50: r2 = "during a platform message callback"
    //     0x944b50: ldr             x2, [PP, #0x3300]  ; [pp+0x3300] "during a platform message callback"
    // 0x944b54: r3 = Instance_GI
    //     0x944b54: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Obj!GI@6a4711
    // 0x944b58: stur            x0, [fp, #-0x88]
    // 0x944b5c: r0 = call 0x305040
    //     0x944b5c: bl              #0x305040
    // 0x944b60: r0 = SI()
    //     0x944b60: bl              #0x8c4f90  ; AllocateSIStub -> SI (size=0x14)
    // 0x944b64: mov             x1, x0
    // 0x944b68: ldur            x0, [fp, #-0x78]
    // 0x944b6c: StoreField: r1->field_7 = r0
    //     0x944b6c: stur            w0, [x1, #7]
    // 0x944b70: ldur            x2, [fp, #-0x80]
    // 0x944b74: StoreField: r1->field_b = r2
    //     0x944b74: stur            w2, [x1, #0xb]
    // 0x944b78: r3 = false
    //     0x944b78: add             x3, NULL, #0x30  ; false
    // 0x944b7c: StoreField: r1->field_f = r3
    //     0x944b7c: stur            w3, [x1, #0xf]
    // 0x944b80: r0 = call 0x300250
    //     0x944b80: bl              #0x300250
    // 0x944b84: ldur            x0, [fp, #-0x20]
    // 0x944b88: mov             x1, x0
    // 0x944b8c: r0 = Null
    //     0x944b8c: mov             x0, NULL
    // 0x944b90: cmp             w1, NULL
    // 0x944b94: b.eq            #0x944bfc
    // 0x944b98: stp             x0, x1, [SP]
    // 0x944b9c: mov             x0, x1
    // 0x944ba0: ClosureCall
    //     0x944ba0: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x944ba4: ldur            x2, [x0, #0x1f]
    //     0x944ba8: blr             x2
    // 0x944bac: r0 = Null
    //     0x944bac: mov             x0, NULL
    // 0x944bb0: r0 = ReturnAsyncNotFuture()
    //     0x944bb0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x944bb4: sub             SP, fp, #0xa0
    // 0x944bb8: mov             x2, x0
    // 0x944bbc: stur            x0, [fp, #-0x78]
    // 0x944bc0: ldur            x0, [fp, #-0x20]
    // 0x944bc4: stur            x1, [fp, #-0x80]
    // 0x944bc8: cmp             w0, NULL
    // 0x944bcc: b.eq            #0x944c00
    // 0x944bd0: stp             NULL, x0, [SP]
    // 0x944bd4: ClosureCall
    //     0x944bd4: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x944bd8: ldur            x2, [x0, #0x1f]
    //     0x944bdc: blr             x2
    // 0x944be0: ldur            x0, [fp, #-0x78]
    // 0x944be4: ldur            x1, [fp, #-0x80]
    // 0x944be8: r0 = ReThrow()
    //     0x944be8: bl              #0x94dce4  ; ReThrowStub
    // 0x944bec: brk             #0
    // 0x944bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944bf0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944bf4: b               #0x944ae0
    // 0x944bf8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x944bf8: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
    // 0x944bfc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x944bfc: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
    // 0x944c00: r0 = NullErrorSharedWithoutFPURegs()
    //     0x944c00: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, ByteData?) {
    // ** addr: 0x944db0, size: 0xac
    // 0x944db0: EnterFrame
    //     0x944db0: stp             fp, lr, [SP, #-0x10]!
    //     0x944db4: mov             fp, SP
    // 0x944db8: AllocStack(0x58)
    //     0x944db8: sub             SP, SP, #0x58
    // 0x944dbc: SetupParameters()
    //     0x944dbc: ldr             x0, [fp, #0x18]
    //     0x944dc0: ldur            w1, [x0, #0x17]
    //     0x944dc4: add             x1, x1, HEAP, lsl #32
    // 0x944dc8: CheckStackOverflow
    //     0x944dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944dcc: cmp             SP, x16
    //     0x944dd0: b.ls            #0x944e54
    // 0x944dd4: LoadField: r0 = r1->field_f
    //     0x944dd4: ldur            w0, [x1, #0xf]
    // 0x944dd8: DecompressPointer r0
    //     0x944dd8: add             x0, x0, HEAP, lsl #32
    // 0x944ddc: ldr             x16, [fp, #0x10]
    // 0x944de0: str             x16, [SP]
    // 0x944de4: mov             x1, x0
    // 0x944de8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x944de8: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x944dec: r0 = call 0x7e4510
    //     0x944dec: bl              #0x7e4510
    // 0x944df0: b               #0x944e44
    // 0x944df4: sub             SP, fp, #0x58
    // 0x944df8: mov             x2, x0
    // 0x944dfc: stur            x0, [fp, #-0x48]
    // 0x944e00: mov             x0, x1
    // 0x944e04: stur            x1, [fp, #-0x50]
    // 0x944e08: r1 = <List<Object>>
    //     0x944e08: ldr             x1, [PP, #0x2468]  ; [pp+0x2468] TypeArguments: <List<Object>>
    // 0x944e0c: r0 = OI()
    //     0x944e0c: bl              #0x8c4f9c  ; AllocateOIStub -> OI (size=0x2c)
    // 0x944e10: mov             x1, x0
    // 0x944e14: r2 = "during a platform message response callback"
    //     0x944e14: ldr             x2, [PP, #0x2a50]  ; [pp+0x2a50] "during a platform message response callback"
    // 0x944e18: r3 = Instance_GI
    //     0x944e18: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Obj!GI@6a4711
    // 0x944e1c: r0 = call 0x305040
    //     0x944e1c: bl              #0x305040
    // 0x944e20: r0 = SI()
    //     0x944e20: bl              #0x8c4f90  ; AllocateSIStub -> SI (size=0x14)
    // 0x944e24: mov             x1, x0
    // 0x944e28: ldur            x0, [fp, #-0x48]
    // 0x944e2c: StoreField: r1->field_7 = r0
    //     0x944e2c: stur            w0, [x1, #7]
    // 0x944e30: ldur            x0, [fp, #-0x50]
    // 0x944e34: StoreField: r1->field_b = r0
    //     0x944e34: stur            w0, [x1, #0xb]
    // 0x944e38: r0 = false
    //     0x944e38: add             x0, NULL, #0x30  ; false
    // 0x944e3c: StoreField: r1->field_f = r0
    //     0x944e3c: stur            w0, [x1, #0xf]
    // 0x944e40: r0 = call 0x300250
    //     0x944e40: bl              #0x300250
    // 0x944e44: r0 = Null
    //     0x944e44: mov             x0, NULL
    // 0x944e48: LeaveFrame
    //     0x944e48: mov             SP, fp
    //     0x944e4c: ldp             fp, lr, [SP], #0x10
    // 0x944e50: ret
    //     0x944e50: ret             
    // 0x944e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944e54: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944e58: b               #0x944dd4
  }
}
