// lib: , url: package:flutter/src/services/platform_channel.dart

// class id: 1049114, size: 0x8
class :: {
}

// class id: 1854, size: 0x14, field offset: 0x8
//   const constructor, 
class uha extends Object {

  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x91b348, size: 0x138
    // 0x91b348: EnterFrame
    //     0x91b348: stp             fp, lr, [SP, #-0x10]!
    //     0x91b34c: mov             fp, SP
    // 0x91b350: AllocStack(0x88)
    //     0x91b350: sub             SP, SP, #0x88
    // 0x91b354: SetupParameters(uha this /* r1, fp-0x60 */)
    //     0x91b354: stur            NULL, [fp, #-8]
    //     0x91b358: movz            x0, #0
    //     0x91b35c: add             x1, fp, w0, sxtw #2
    //     0x91b360: ldr             x1, [x1, #0x10]
    //     0x91b364: stur            x1, [fp, #-0x60]
    //     0x91b368: ldur            w2, [x1, #0x17]
    //     0x91b36c: add             x2, x2, HEAP, lsl #32
    //     0x91b370: stur            x2, [fp, #-0x58]
    // 0x91b374: CheckStackOverflow
    //     0x91b374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b378: cmp             SP, x16
    //     0x91b37c: b.ls            #0x91b478
    // 0x91b380: InitAsync() -> Future<void?>
    //     0x91b380: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x91b384: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91b388: r0 = call 0x2ffbc8
    //     0x91b388: bl              #0x2ffbc8
    // 0x91b38c: ldur            x0, [fp, #-0x58]
    // 0x91b390: LoadField: r1 = r0->field_f
    //     0x91b390: ldur            w1, [x0, #0xf]
    // 0x91b394: DecompressPointer r1
    //     0x91b394: add             x1, x1, HEAP, lsl #32
    // 0x91b398: LoadField: r2 = r1->field_7
    //     0x91b398: ldur            w2, [x1, #7]
    // 0x91b39c: DecompressPointer r2
    //     0x91b39c: add             x2, x2, HEAP, lsl #32
    // 0x91b3a0: r1 = Instance__Kga
    //     0x91b3a0: ldr             x1, [PP, #0x2a18]  ; [pp+0x2a18] Obj!_Kga@692661
    // 0x91b3a4: r3 = Null
    //     0x91b3a4: mov             x3, NULL
    // 0x91b3a8: r0 = call 0x8200f8
    //     0x91b3a8: bl              #0x8200f8
    // 0x91b3ac: ldur            x0, [fp, #-0x58]
    // 0x91b3b0: LoadField: r1 = r0->field_13
    //     0x91b3b0: ldur            w1, [x0, #0x13]
    // 0x91b3b4: DecompressPointer r1
    //     0x91b3b4: add             x1, x1, HEAP, lsl #32
    // 0x91b3b8: r16 = <void?>
    //     0x91b3b8: ldr             x16, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    // 0x91b3bc: stp             x1, x16, [SP, #0x10]
    // 0x91b3c0: r16 = "cancel"
    //     0x91b3c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e688] "cancel"
    //     0x91b3c4: ldr             x16, [x16, #0x688]
    // 0x91b3c8: stp             NULL, x16, [SP]
    // 0x91b3cc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x91b3cc: ldr             x4, [PP, #0x4f0]  ; [pp+0x4f0] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x91b3d0: r0 = call 0x8215b8
    //     0x91b3d0: bl              #0x8215b8
    // 0x91b3d4: mov             x1, x0
    // 0x91b3d8: stur            x1, [fp, #-0x60]
    // 0x91b3dc: r0 = Await()
    //     0x91b3dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x91b3e0: b               #0x91b470
    // 0x91b3e4: sub             SP, fp, #0x88
    // 0x91b3e8: mov             x3, x0
    // 0x91b3ec: stur            x0, [fp, #-0x58]
    // 0x91b3f0: mov             x0, x1
    // 0x91b3f4: stur            x1, [fp, #-0x60]
    // 0x91b3f8: r1 = Null
    //     0x91b3f8: mov             x1, NULL
    // 0x91b3fc: r2 = 4
    //     0x91b3fc: movz            x2, #0x4
    // 0x91b400: r0 = AllocateArray()
    //     0x91b400: bl              #0x94fa24  ; AllocateArrayStub
    // 0x91b404: r17 = "while de-activating platform stream on channel "
    //     0x91b404: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e690] "while de-activating platform stream on channel "
    //     0x91b408: ldr             x17, [x17, #0x690]
    // 0x91b40c: StoreField: r0->field_f = r17
    //     0x91b40c: stur            w17, [x0, #0xf]
    // 0x91b410: ldur            x1, [fp, #-0x20]
    // 0x91b414: LoadField: r2 = r1->field_f
    //     0x91b414: ldur            w2, [x1, #0xf]
    // 0x91b418: DecompressPointer r2
    //     0x91b418: add             x2, x2, HEAP, lsl #32
    // 0x91b41c: LoadField: r1 = r2->field_7
    //     0x91b41c: ldur            w1, [x2, #7]
    // 0x91b420: DecompressPointer r1
    //     0x91b420: add             x1, x1, HEAP, lsl #32
    // 0x91b424: StoreField: r0->field_13 = r1
    //     0x91b424: stur            w1, [x0, #0x13]
    // 0x91b428: str             x0, [SP]
    // 0x91b42c: r0 = _interpolate()
    //     0x91b42c: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x91b430: r1 = <List<Object>>
    //     0x91b430: ldr             x1, [PP, #0x2468]  ; [pp+0x2468] TypeArguments: <List<Object>>
    // 0x91b434: stur            x0, [fp, #-0x68]
    // 0x91b438: r0 = OI()
    //     0x91b438: bl              #0x8c4f9c  ; AllocateOIStub -> OI (size=0x2c)
    // 0x91b43c: mov             x1, x0
    // 0x91b440: ldur            x2, [fp, #-0x68]
    // 0x91b444: r3 = Instance_GI
    //     0x91b444: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Obj!GI@6a4711
    // 0x91b448: r0 = call 0x305040
    //     0x91b448: bl              #0x305040
    // 0x91b44c: r0 = SI()
    //     0x91b44c: bl              #0x8c4f90  ; AllocateSIStub -> SI (size=0x14)
    // 0x91b450: mov             x1, x0
    // 0x91b454: ldur            x0, [fp, #-0x58]
    // 0x91b458: StoreField: r1->field_7 = r0
    //     0x91b458: stur            w0, [x1, #7]
    // 0x91b45c: ldur            x0, [fp, #-0x60]
    // 0x91b460: StoreField: r1->field_b = r0
    //     0x91b460: stur            w0, [x1, #0xb]
    // 0x91b464: r0 = false
    //     0x91b464: add             x0, NULL, #0x30  ; false
    // 0x91b468: StoreField: r1->field_f = r0
    //     0x91b468: stur            w0, [x1, #0xf]
    // 0x91b46c: r0 = call 0x300250
    //     0x91b46c: bl              #0x300250
    // 0x91b470: r0 = Null
    //     0x91b470: mov             x0, NULL
    // 0x91b474: r0 = ReturnAsyncNotFuture()
    //     0x91b474: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91b478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b478: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b47c: b               #0x91b380
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x91b480, size: 0x150
    // 0x91b480: EnterFrame
    //     0x91b480: stp             fp, lr, [SP, #-0x10]!
    //     0x91b484: mov             fp, SP
    // 0x91b488: AllocStack(0x88)
    //     0x91b488: sub             SP, SP, #0x88
    // 0x91b48c: SetupParameters(uha this /* r1, fp-0x60 */)
    //     0x91b48c: stur            NULL, [fp, #-8]
    //     0x91b490: movz            x0, #0
    //     0x91b494: add             x1, fp, w0, sxtw #2
    //     0x91b498: ldr             x1, [x1, #0x10]
    //     0x91b49c: stur            x1, [fp, #-0x60]
    //     0x91b4a0: ldur            w2, [x1, #0x17]
    //     0x91b4a4: add             x2, x2, HEAP, lsl #32
    //     0x91b4a8: stur            x2, [fp, #-0x58]
    // 0x91b4ac: CheckStackOverflow
    //     0x91b4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b4b0: cmp             SP, x16
    //     0x91b4b4: b.ls            #0x91b5c8
    // 0x91b4b8: InitAsync() -> Future<void?>
    //     0x91b4b8: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x91b4bc: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91b4c0: r0 = call 0x2ffbc8
    //     0x91b4c0: bl              #0x2ffbc8
    // 0x91b4c4: ldur            x0, [fp, #-0x58]
    // 0x91b4c8: LoadField: r1 = r0->field_f
    //     0x91b4c8: ldur            w1, [x0, #0xf]
    // 0x91b4cc: DecompressPointer r1
    //     0x91b4cc: add             x1, x1, HEAP, lsl #32
    // 0x91b4d0: LoadField: r3 = r1->field_7
    //     0x91b4d0: ldur            w3, [x1, #7]
    // 0x91b4d4: DecompressPointer r3
    //     0x91b4d4: add             x3, x3, HEAP, lsl #32
    // 0x91b4d8: mov             x2, x0
    // 0x91b4dc: stur            x3, [fp, #-0x60]
    // 0x91b4e0: r1 = Function '<anonymous closure>':.
    //     0x91b4e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e698] AnonymousClosure: (0x91b5d0), in [package:flutter/src/services/platform_channel.dart] uha::<anonymous closure> (0x91b480)
    //     0x91b4e4: ldr             x1, [x1, #0x698]
    // 0x91b4e8: r0 = AllocateClosure()
    //     0x91b4e8: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91b4ec: ldur            x2, [fp, #-0x60]
    // 0x91b4f0: mov             x3, x0
    // 0x91b4f4: r1 = Instance__Kga
    //     0x91b4f4: ldr             x1, [PP, #0x2a18]  ; [pp+0x2a18] Obj!_Kga@692661
    // 0x91b4f8: r0 = call 0x8200f8
    //     0x91b4f8: bl              #0x8200f8
    // 0x91b4fc: ldur            x0, [fp, #-0x58]
    // 0x91b500: LoadField: r1 = r0->field_13
    //     0x91b500: ldur            w1, [x0, #0x13]
    // 0x91b504: DecompressPointer r1
    //     0x91b504: add             x1, x1, HEAP, lsl #32
    // 0x91b508: r16 = <void?>
    //     0x91b508: ldr             x16, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    // 0x91b50c: stp             x1, x16, [SP, #0x10]
    // 0x91b510: r16 = "listen"
    //     0x91b510: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e6a0] "listen"
    //     0x91b514: ldr             x16, [x16, #0x6a0]
    // 0x91b518: stp             NULL, x16, [SP]
    // 0x91b51c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x91b51c: ldr             x4, [PP, #0x4f0]  ; [pp+0x4f0] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x91b520: r0 = call 0x8215b8
    //     0x91b520: bl              #0x8215b8
    // 0x91b524: mov             x1, x0
    // 0x91b528: stur            x1, [fp, #-0x60]
    // 0x91b52c: r0 = Await()
    //     0x91b52c: bl              #0x8c1bb8  ; AwaitStub
    // 0x91b530: b               #0x91b5c0
    // 0x91b534: sub             SP, fp, #0x88
    // 0x91b538: mov             x3, x0
    // 0x91b53c: stur            x0, [fp, #-0x58]
    // 0x91b540: mov             x0, x1
    // 0x91b544: stur            x1, [fp, #-0x60]
    // 0x91b548: r1 = Null
    //     0x91b548: mov             x1, NULL
    // 0x91b54c: r2 = 4
    //     0x91b54c: movz            x2, #0x4
    // 0x91b550: r0 = AllocateArray()
    //     0x91b550: bl              #0x94fa24  ; AllocateArrayStub
    // 0x91b554: r17 = "while activating platform stream on channel "
    //     0x91b554: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e6a8] "while activating platform stream on channel "
    //     0x91b558: ldr             x17, [x17, #0x6a8]
    // 0x91b55c: StoreField: r0->field_f = r17
    //     0x91b55c: stur            w17, [x0, #0xf]
    // 0x91b560: ldur            x1, [fp, #-0x20]
    // 0x91b564: LoadField: r2 = r1->field_f
    //     0x91b564: ldur            w2, [x1, #0xf]
    // 0x91b568: DecompressPointer r2
    //     0x91b568: add             x2, x2, HEAP, lsl #32
    // 0x91b56c: LoadField: r1 = r2->field_7
    //     0x91b56c: ldur            w1, [x2, #7]
    // 0x91b570: DecompressPointer r1
    //     0x91b570: add             x1, x1, HEAP, lsl #32
    // 0x91b574: StoreField: r0->field_13 = r1
    //     0x91b574: stur            w1, [x0, #0x13]
    // 0x91b578: str             x0, [SP]
    // 0x91b57c: r0 = _interpolate()
    //     0x91b57c: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x91b580: r1 = <List<Object>>
    //     0x91b580: ldr             x1, [PP, #0x2468]  ; [pp+0x2468] TypeArguments: <List<Object>>
    // 0x91b584: stur            x0, [fp, #-0x68]
    // 0x91b588: r0 = OI()
    //     0x91b588: bl              #0x8c4f9c  ; AllocateOIStub -> OI (size=0x2c)
    // 0x91b58c: mov             x1, x0
    // 0x91b590: ldur            x2, [fp, #-0x68]
    // 0x91b594: r3 = Instance_GI
    //     0x91b594: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Obj!GI@6a4711
    // 0x91b598: r0 = call 0x305040
    //     0x91b598: bl              #0x305040
    // 0x91b59c: r0 = SI()
    //     0x91b59c: bl              #0x8c4f90  ; AllocateSIStub -> SI (size=0x14)
    // 0x91b5a0: mov             x1, x0
    // 0x91b5a4: ldur            x0, [fp, #-0x58]
    // 0x91b5a8: StoreField: r1->field_7 = r0
    //     0x91b5a8: stur            w0, [x1, #7]
    // 0x91b5ac: ldur            x0, [fp, #-0x60]
    // 0x91b5b0: StoreField: r1->field_b = r0
    //     0x91b5b0: stur            w0, [x1, #0xb]
    // 0x91b5b4: r0 = false
    //     0x91b5b4: add             x0, NULL, #0x30  ; false
    // 0x91b5b8: StoreField: r1->field_f = r0
    //     0x91b5b8: stur            w0, [x1, #0xf]
    // 0x91b5bc: r0 = call 0x300250
    //     0x91b5bc: bl              #0x300250
    // 0x91b5c0: r0 = Null
    //     0x91b5c0: mov             x0, NULL
    // 0x91b5c4: r0 = ReturnAsyncNotFuture()
    //     0x91b5c4: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91b5c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b5c8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b5cc: b               #0x91b4b8
  }
  [closure] Future<Null> <anonymous closure>(dynamic, ByteData?) async {
    // ** addr: 0x91b5d0, size: 0x160
    // 0x91b5d0: EnterFrame
    //     0x91b5d0: stp             fp, lr, [SP, #-0x10]!
    //     0x91b5d4: mov             fp, SP
    // 0x91b5d8: AllocStack(0x68)
    //     0x91b5d8: sub             SP, SP, #0x68
    // 0x91b5dc: SetupParameters(uha this /* r1, fp-0x60 */, dynamic _ /* r2, fp-0x58 */)
    //     0x91b5dc: stur            NULL, [fp, #-8]
    //     0x91b5e0: movz            x0, #0
    //     0x91b5e4: add             x1, fp, w0, sxtw #2
    //     0x91b5e8: ldr             x1, [x1, #0x18]
    //     0x91b5ec: stur            x1, [fp, #-0x60]
    //     0x91b5f0: add             x2, fp, w0, sxtw #2
    //     0x91b5f4: ldr             x2, [x2, #0x10]
    //     0x91b5f8: stur            x2, [fp, #-0x58]
    //     0x91b5fc: ldur            w3, [x1, #0x17]
    //     0x91b600: add             x3, x3, HEAP, lsl #32
    //     0x91b604: stur            x3, [fp, #-0x50]
    // 0x91b608: CheckStackOverflow
    //     0x91b608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b60c: cmp             SP, x16
    //     0x91b610: b.ls            #0x91b728
    // 0x91b614: InitAsync() -> Future<Null?>
    //     0x91b614: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x91b618: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91b61c: ldur            x2, [fp, #-0x58]
    // 0x91b620: cmp             w2, NULL
    // 0x91b624: b.ne            #0x91b660
    // 0x91b628: ldur            x0, [fp, #-0x50]
    // 0x91b62c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91b62c: ldur            w1, [x0, #0x17]
    // 0x91b630: DecompressPointer r1
    //     0x91b630: add             x1, x1, HEAP, lsl #32
    // 0x91b634: r16 = Sentinel
    //     0x91b634: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91b638: cmp             w1, w16
    // 0x91b63c: b.ne            #0x91b64c
    // 0x91b640: r16 = "controller"
    //     0x91b640: ldr             x16, [PP, #0x3e30]  ; [pp+0x3e30] "controller"
    // 0x91b644: str             x16, [SP]
    // 0x91b648: r0 = _throwLocalNotInitialized()
    //     0x91b648: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91b64c: ldur            x0, [fp, #-0x50]
    // 0x91b650: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91b650: ldur            w1, [x0, #0x17]
    // 0x91b654: DecompressPointer r1
    //     0x91b654: add             x1, x1, HEAP, lsl #32
    // 0x91b658: r0 = call 0x751f04
    //     0x91b658: bl              #0x751f04
    // 0x91b65c: b               #0x91b714
    // 0x91b660: ldur            x0, [fp, #-0x50]
    // 0x91b664: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91b664: ldur            w1, [x0, #0x17]
    // 0x91b668: DecompressPointer r1
    //     0x91b668: add             x1, x1, HEAP, lsl #32
    // 0x91b66c: r16 = Sentinel
    //     0x91b66c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91b670: cmp             w1, w16
    // 0x91b674: b.ne            #0x91b684
    // 0x91b678: r16 = "controller"
    //     0x91b678: ldr             x16, [PP, #0x3e30]  ; [pp+0x3e30] "controller"
    // 0x91b67c: str             x16, [SP]
    // 0x91b680: r0 = _throwLocalNotInitialized()
    //     0x91b680: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91b684: ldur            x0, [fp, #-0x50]
    // 0x91b688: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x91b688: ldur            w3, [x0, #0x17]
    // 0x91b68c: DecompressPointer r3
    //     0x91b68c: add             x3, x3, HEAP, lsl #32
    // 0x91b690: ldur            x2, [fp, #-0x58]
    // 0x91b694: stur            x3, [fp, #-0x60]
    // 0x91b698: r1 = Instance_iha
    //     0x91b698: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!iha@6925a1
    //     0x91b69c: ldr             x1, [x1, #0x668]
    // 0x91b6a0: r0 = call 0x821134
    //     0x91b6a0: bl              #0x821134
    // 0x91b6a4: ldur            x1, [fp, #-0x60]
    // 0x91b6a8: mov             x2, x0
    // 0x91b6ac: r0 = call 0x77507c
    //     0x91b6ac: bl              #0x77507c
    // 0x91b6b0: b               #0x91b714
    // 0x91b6b4: sub             SP, fp, #0x68
    // 0x91b6b8: mov             x2, x0
    // 0x91b6bc: stur            x0, [fp, #-0x50]
    // 0x91b6c0: r0 = 59
    //     0x91b6c0: movz            x0, #0x3b
    // 0x91b6c4: branchIfSmi(r2, 0x91b6d0)
    //     0x91b6c4: tbz             w2, #0, #0x91b6d0
    // 0x91b6c8: r0 = LoadClassIdInstr(r2)
    //     0x91b6c8: ldur            x0, [x2, #-1]
    //     0x91b6cc: ubfx            x0, x0, #0xc, #0x14
    // 0x91b6d0: cmp             x0, #0x75f
    // 0x91b6d4: b.ne            #0x91b71c
    // 0x91b6d8: ldur            x0, [fp, #-0x28]
    // 0x91b6dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91b6dc: ldur            w1, [x0, #0x17]
    // 0x91b6e0: DecompressPointer r1
    //     0x91b6e0: add             x1, x1, HEAP, lsl #32
    // 0x91b6e4: r16 = Sentinel
    //     0x91b6e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91b6e8: cmp             w1, w16
    // 0x91b6ec: b.ne            #0x91b6fc
    // 0x91b6f0: r16 = "controller"
    //     0x91b6f0: ldr             x16, [PP, #0x3e30]  ; [pp+0x3e30] "controller"
    // 0x91b6f4: str             x16, [SP]
    // 0x91b6f8: r0 = _throwLocalNotInitialized()
    //     0x91b6f8: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91b6fc: ldur            x0, [fp, #-0x28]
    // 0x91b700: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91b700: ldur            w1, [x0, #0x17]
    // 0x91b704: DecompressPointer r1
    //     0x91b704: add             x1, x1, HEAP, lsl #32
    // 0x91b708: ldur            x2, [fp, #-0x50]
    // 0x91b70c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91b70c: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91b710: r0 = call 0x77fcac
    //     0x91b710: bl              #0x77fcac
    // 0x91b714: r0 = Null
    //     0x91b714: mov             x0, NULL
    // 0x91b718: r0 = ReturnAsyncNotFuture()
    //     0x91b718: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91b71c: ldur            x0, [fp, #-0x50]
    // 0x91b720: r0 = ReThrow()
    //     0x91b720: bl              #0x94dce4  ; ReThrowStub
    // 0x91b724: brk             #0
    // 0x91b728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b728: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b72c: b               #0x91b614
  }
}

// class id: 1855, size: 0x14, field offset: 0x8
//   const constructor, 
class MethodChannel extends Object {

  _OneByteString field_8;
  iha field_c;

  [closure] Future<ByteData?> <anonymous closure>(dynamic, ByteData?) {
    // ** addr: 0x36c8d8, size: -0x1
  }
}

// class id: 1856, size: 0x14, field offset: 0x14
//   const constructor, 
class tha extends MethodChannel {

  _OneByteString field_8;
  gha field_c;
}

// class id: 1857, size: 0x18, field offset: 0x8
//   const constructor, 
class sha<X0> extends Object {

  _OneByteString field_c;
  fha field_10;

  [closure] Future<ByteData?> <anonymous closure>(dynamic, ByteData?) async {
    // ** addr: 0x916028, size: 0xe4
    // 0x916028: EnterFrame
    //     0x916028: stp             fp, lr, [SP, #-0x10]!
    //     0x91602c: mov             fp, SP
    // 0x916030: AllocStack(0x38)
    //     0x916030: sub             SP, SP, #0x38
    // 0x916034: SetupParameters(sha<X0> this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x916034: stur            NULL, [fp, #-8]
    //     0x916038: movz            x0, #0
    //     0x91603c: add             x1, fp, w0, sxtw #2
    //     0x916040: ldr             x1, [x1, #0x18]
    //     0x916044: add             x2, fp, w0, sxtw #2
    //     0x916048: ldr             x2, [x2, #0x10]
    //     0x91604c: stur            x2, [fp, #-0x18]
    //     0x916050: ldur            w3, [x1, #0x17]
    //     0x916054: add             x3, x3, HEAP, lsl #32
    //     0x916058: stur            x3, [fp, #-0x10]
    // 0x91605c: CheckStackOverflow
    //     0x91605c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x916060: cmp             SP, x16
    //     0x916064: b.ls            #0x916104
    // 0x916068: InitAsync() -> Future<ByteData?>
    //     0x916068: ldr             x0, [PP, #0x5e8]  ; [pp+0x5e8] TypeArguments: <ByteData?>
    //     0x91606c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x916070: ldur            x3, [fp, #-0x10]
    // 0x916074: LoadField: r0 = r3->field_f
    //     0x916074: ldur            w0, [x3, #0xf]
    // 0x916078: DecompressPointer r0
    //     0x916078: add             x0, x0, HEAP, lsl #32
    // 0x91607c: LoadField: r4 = r0->field_f
    //     0x91607c: ldur            w4, [x0, #0xf]
    // 0x916080: DecompressPointer r4
    //     0x916080: add             x4, x4, HEAP, lsl #32
    // 0x916084: stur            x4, [fp, #-0x28]
    // 0x916088: LoadField: r5 = r3->field_13
    //     0x916088: ldur            w5, [x3, #0x13]
    // 0x91608c: DecompressPointer r5
    //     0x91608c: add             x5, x5, HEAP, lsl #32
    // 0x916090: stur            x5, [fp, #-0x20]
    // 0x916094: r0 = LoadClassIdInstr(r4)
    //     0x916094: ldur            x0, [x4, #-1]
    //     0x916098: ubfx            x0, x0, #0xc, #0x14
    // 0x91609c: mov             x1, x4
    // 0x9160a0: ldur            x2, [fp, #-0x18]
    // 0x9160a4: r0 = GDT[cid_x0 + 0x352e]()
    //     0x9160a4: movz            x17, #0x352e
    //     0x9160a8: add             lr, x0, x17
    //     0x9160ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9160b0: blr             lr
    // 0x9160b4: ldur            x16, [fp, #-0x20]
    // 0x9160b8: stp             x0, x16, [SP]
    // 0x9160bc: ldur            x0, [fp, #-0x20]
    // 0x9160c0: ClosureCall
    //     0x9160c0: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9160c4: ldur            x2, [x0, #0x1f]
    //     0x9160c8: blr             x2
    // 0x9160cc: mov             x1, x0
    // 0x9160d0: stur            x1, [fp, #-0x18]
    // 0x9160d4: r0 = Await()
    //     0x9160d4: bl              #0x8c1bb8  ; AwaitStub
    // 0x9160d8: ldur            x1, [fp, #-0x28]
    // 0x9160dc: r2 = LoadClassIdInstr(r1)
    //     0x9160dc: ldur            x2, [x1, #-1]
    //     0x9160e0: ubfx            x2, x2, #0xc, #0x14
    // 0x9160e4: mov             x16, x0
    // 0x9160e8: mov             x0, x2
    // 0x9160ec: mov             x2, x16
    // 0x9160f0: r0 = GDT[cid_x0 + 0x188f]()
    //     0x9160f0: movz            x17, #0x188f
    //     0x9160f4: add             lr, x0, x17
    //     0x9160f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9160fc: blr             lr
    // 0x916100: r0 = ReturnAsyncNotFuture()
    //     0x916100: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x916104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916104: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916108: b               #0x916068
  }
}
